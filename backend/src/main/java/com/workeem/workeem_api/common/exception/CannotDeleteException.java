package com.workeem.workeem_api.common.exception;

/**
 * Exception lancée quand une entité ne peut pas être supprimée à cause de relations existantes
 */
public class CannotDeleteException extends RuntimeException {
    
    public CannotDeleteException(String message) {
        super(message);
    }
    
    public CannotDeleteException(String message, Throwable cause) {
        super(message, cause);
    }
}
