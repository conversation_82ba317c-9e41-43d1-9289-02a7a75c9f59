package com.workeem.workeem_api.business.reservation.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

public class ValidMinuteStepValidator implements ConstraintValidator<ValidMinuteStep, Instant> {
    
    private int step;
    
    @Override
    public void initialize(ValidMinuteStep constraintAnnotation) {
        this.step = constraintAnnotation.step();
    }
    
    @Override
    public boolean isValid(Instant instant, ConstraintValidatorContext context) {
        if (instant == null) {
            return true; // Laisser @NotNull gérer la validation de nullité
        }

        // Convertir Instant en LocalDateTime
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());

        // Vérifier que les minutes sont des multiples du pas spécifié
        boolean validMinutes = dateTime.getMinute() % step == 0;

        // Vérifier que les secondes sont à 0
        boolean validSeconds = dateTime.getSecond() == 0;

        return validMinutes && validSeconds;
    }
}
