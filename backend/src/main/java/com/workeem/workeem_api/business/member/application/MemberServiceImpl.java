package com.workeem.workeem_api.business.member.application;

import com.workeem.workeem_api.business.member.domain.Member;
import com.workeem.workeem_api.business.member.domain.MemberType;
import com.workeem.workeem_api.business.member.domain.MemberStatus;
import com.workeem.workeem_api.business.member.infrastructure.MemberRepository;
import com.workeem.workeem_api.business.reservation.infrastructure.ReservationRepository;
import com.workeem.workeem_api.common.exception.CannotDeleteException;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class MemberServiceImpl implements MemberService {

    private final MemberRepository memberRepository;
    private final ReservationRepository reservationRepository;

    @Override
    @Transactional(readOnly = true)
    public List<Member> getAllMembers(Long siteId) {
        log.debug("Fetching all members for site: {}", siteId);
        return memberRepository.findBySiteId(siteId);
    }

    @Override
    @Transactional(readOnly = true)
    public Member getMemberById(Long id) {
        log.debug("Fetching member with ID: {}", id);
        return memberRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Member with ID " + id + " not found"));
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Member> getMemberByEmail(Long siteId, String email) {
        log.debug("Fetching member by email: {} in site: {}", email, siteId);
        return memberRepository.findBySiteIdAndEmail(siteId, email);
    }

    @Override
    public Member createMember(Member member) {
        log.debug("Creating new member: {} {} for site: {}", 
                  member.getFirstName(), member.getLastName(), member.getSiteId());

        // Vérifier si l'email existe déjà (seulement pour les vrais emails, pas les temporaires)
        if (member.getEmail() != null && !member.getEmail().contains("@temp.com") &&
            emailExists(member.getSiteId(), member.getEmail())) {
            throw new IllegalArgumentException("Un membre avec cet email existe déjà sur ce site");
        }

        return memberRepository.save(member);
    }

    @Override
    public Member updateMember(Long memberId, Member member) {
        log.debug("Updating member with ID: {}", memberId);

        Member existingMember = getMemberById(memberId);

        // Vérifier si l'email existe déjà (en excluant le member actuel)
        Optional<Member> memberWithSameEmail = memberRepository.findBySiteIdAndEmail(member.getSiteId(), member.getEmail());
        if (memberWithSameEmail.isPresent() && !memberWithSameEmail.get().getMemberId().equals(memberId)) {
            throw new IllegalArgumentException("Un membre avec cet email existe déjà sur ce site");
        }

        // Mettre à jour les champs
        existingMember.setFirstName(member.getFirstName());
        existingMember.setLastName(member.getLastName());
        existingMember.setEmail(member.getEmail());
        existingMember.setPhone(member.getPhone());
        existingMember.setCompany(member.getCompany());
        existingMember.setMemberType(member.getMemberType());
        existingMember.setStudentCode(member.getStudentCode());
        existingMember.setIceNumber(member.getIceNumber());
        existingMember.setSubscriptionId(member.getSubscriptionId());
        existingMember.setIsActive(member.getIsActive());

        return memberRepository.save(existingMember);
    }

    @Override
    public void deleteMember(Long id) {
        log.debug("Deleting member with ID: {}", id);

        // Vérifier si le membre a des réservations
        if (reservationRepository.existsByMember_MemberId(id)) {
            throw new CannotDeleteException("Impossible de supprimer ce membre car il a des réservations associées. Veuillez d'abord supprimer ou réassigner les réservations.");
        }

        Member member = getMemberById(id);
        memberRepository.delete(member);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean emailExists(Long siteId, String email) {
        return memberRepository.existsBySiteIdAndEmail(siteId, email);
    }

    @Override
    @Transactional(readOnly = true)
    public long countMembersBySite(Long siteId) {
        return memberRepository.countBySiteId(siteId);
    }

    @Override
    @Transactional(readOnly = true)
    public long countActiveMembersBySite(Long siteId) {
        return memberRepository.countBySiteIdAndStatus(siteId, MemberStatus.ACTIVE);
    }

    @Override
    @Transactional(readOnly = true)
    public long countNewMembersThisWeek(Long siteId) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfWeek = now.with(DayOfWeek.MONDAY).toLocalDate().atStartOfDay();
        LocalDateTime endOfWeek = startOfWeek.plusDays(6).withHour(23).withMinute(59).withSecond(59);

        return memberRepository.countNewMembersThisWeek(siteId, startOfWeek, endOfWeek);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Member> getAllMembers(Long siteId, Pageable pageable) {
        log.debug("Fetching all members for site: {} with pagination: {}", siteId, pageable);

        // Vérifier si le tri est par lastModifiedDate pour utiliser COALESCE
        if (pageable.getSort().isSorted()) {
            for (Sort.Order order : pageable.getSort()) {
                if ("lastModifiedDate".equals(order.getProperty())) {
                    if (order.getDirection() == Sort.Direction.DESC) {
                        return memberRepository.findBySiteIdOrderByModifiedDateDesc(siteId,
                            PageRequest.of(pageable.getPageNumber(), pageable.getPageSize()));
                    } else {
                        return memberRepository.findBySiteIdOrderByModifiedDateAsc(siteId,
                            PageRequest.of(pageable.getPageNumber(), pageable.getPageSize()));
                    }
                }
            }
        }

        return memberRepository.findBySiteId(siteId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Member> getMembersByStatus(Long siteId, MemberStatus status, Pageable pageable) {
        log.debug("Fetching members by status: {} for site: {} with pagination: {}", status, siteId, pageable);
        return memberRepository.findBySiteIdAndStatus(siteId, status, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Member> searchMembers(Long siteId, String searchTerm, MemberStatus status, MemberType memberType, Long subscriptionId, Pageable pageable) {
        log.debug("Searching members with term: '{}' for site: {} with filters - status: {}, memberType: {}, subscriptionId: {}, pagination: {}",
                  searchTerm, siteId, status, memberType, subscriptionId, pageable);

        // Vérifier si le tri est par lastModifiedDate pour utiliser COALESCE
        if (pageable.getSort().isSorted()) {
            for (Sort.Order order : pageable.getSort()) {
                if ("lastModifiedDate".equals(order.getProperty())) {
                    if (order.getDirection() == Sort.Direction.DESC) {
                        return memberRepository.searchMembersOrderByModifiedDateDesc(siteId, searchTerm, status, memberType, subscriptionId,
                            PageRequest.of(pageable.getPageNumber(), pageable.getPageSize()));
                    } else {
                        return memberRepository.searchMembersOrderByModifiedDateAsc(siteId, searchTerm, status, memberType, subscriptionId,
                            PageRequest.of(pageable.getPageNumber(), pageable.getPageSize()));
                    }
                }
            }
        }

        return memberRepository.searchMembers(siteId, searchTerm, status, memberType, subscriptionId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Member> getMembersWithFilters(Long siteId, MemberStatus status, MemberType memberType, Long subscriptionId, Pageable pageable) {
        log.debug("Fetching members for site: {} with filters - status: {}, memberType: {}, subscriptionId: {}, pagination: {}",
                  siteId, status, memberType, subscriptionId, pageable);

        // Vérifier si le tri est par lastModifiedDate pour utiliser COALESCE
        if (pageable.getSort().isSorted()) {
            for (Sort.Order order : pageable.getSort()) {
                if ("lastModifiedDate".equals(order.getProperty())) {
                    if (order.getDirection() == Sort.Direction.DESC) {
                        return memberRepository.findMembersWithFiltersOrderByModifiedDateDesc(siteId, status, memberType, subscriptionId,
                            PageRequest.of(pageable.getPageNumber(), pageable.getPageSize()));
                    } else {
                        return memberRepository.findMembersWithFiltersOrderByModifiedDateAsc(siteId, status, memberType, subscriptionId,
                            PageRequest.of(pageable.getPageNumber(), pageable.getPageSize()));
                    }
                }
            }
        }

        return memberRepository.findMembersWithFilters(siteId, status, memberType, subscriptionId, pageable);
    }
}
