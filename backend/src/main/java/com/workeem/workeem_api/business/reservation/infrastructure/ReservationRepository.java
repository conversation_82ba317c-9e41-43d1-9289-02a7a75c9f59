package com.workeem.workeem_api.business.reservation.infrastructure;

import com.workeem.workeem_api.business.reservation.domain.Reservation;
import com.workeem.workeem_api.business.reservation.domain.ReservationStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ReservationRepository extends JpaRepository<Reservation, Long> {

    /**
     * Find all reservations for a specific site
     */
    List<Reservation> findBySiteId(Long siteId);

    /**
     * Find all reservations for a specific site with relations loaded
     */
    @Query("SELECT r FROM Reservation r " +
           "LEFT JOIN FETCH r.space " +
           "LEFT JOIN FETCH r.member " +
           "WHERE r.siteId = :siteId")
    List<Reservation> findBySiteIdWithRelations(@Param("siteId") Long siteId);

    /**
     * Find reservations by site and status
     */
    List<Reservation> findBySiteIdAndStatus(Long siteId, ReservationStatus status);

    /**
     * Find reservations by site and space
     */
    List<Reservation> findBySiteIdAndSpace_SpaceId(Long siteId, Long spaceId);

    /**
     * Find reservations by site, space and status
     */
    List<Reservation> findBySiteIdAndSpace_SpaceIdAndStatus(Long siteId, Long spaceId, ReservationStatus status);

    /**
     * Find reservations by member
     */
    List<Reservation> findBySiteIdAndMember_MemberId(Long siteId, Long memberId);

    /**
     * Find reservations in a date range
     */
    @Query("SELECT r FROM Reservation r WHERE r.siteId = :siteId AND " +
           "((r.startTime >= :startDate AND r.startTime <= :endDate) OR " +
           "(r.endTime >= :startDate AND r.endTime <= :endDate) OR " +
           "(r.startTime <= :startDate AND r.endTime >= :endDate))")
    List<Reservation> findBySiteIdAndDateRange(@Param("siteId") Long siteId,
                                               @Param("startDate") LocalDateTime startDate,
                                               @Param("endDate") LocalDateTime endDate);

    /**
     * Find reservations in a date range with relations loaded
     */
    @Query("SELECT r FROM Reservation r " +
           "LEFT JOIN FETCH r.space " +
           "LEFT JOIN FETCH r.member " +
           "WHERE r.siteId = :siteId AND " +
           "((r.startTime >= :startDate AND r.startTime <= :endDate) OR " +
           "(r.endTime >= :startDate AND r.endTime <= :endDate) OR " +
           "(r.startTime <= :startDate AND r.endTime >= :endDate))")
    List<Reservation> findBySiteIdAndDateRangeWithRelations(@Param("siteId") Long siteId,
                                                            @Param("startDate") LocalDateTime startDate,
                                                            @Param("endDate") LocalDateTime endDate);

    /**
     * Find reservations by space in a date range
     */
    @Query("SELECT r FROM Reservation r WHERE r.siteId = :siteId AND r.space.spaceId = :spaceId AND " +
           "((r.startTime >= :startDate AND r.startTime <= :endDate) OR " +
           "(r.endTime >= :startDate AND r.endTime <= :endDate) OR " +
           "(r.startTime <= :startDate AND r.endTime >= :endDate))")
    List<Reservation> findBySiteIdAndSpaceIdAndDateRange(@Param("siteId") Long siteId,
                                                         @Param("spaceId") Long spaceId,
                                                         @Param("startDate") LocalDateTime startDate,
                                                         @Param("endDate") LocalDateTime endDate);

    /**
     * Check for conflicting reservations (time overlap only)
     */
    @Query("SELECT r FROM Reservation r WHERE r.siteId = :siteId AND r.space.spaceId = :spaceId AND " +
           "r.status NOT IN ('CANCELLED') AND " +
           "((r.startTime < :endTime AND r.endTime > :startTime))")
    List<Reservation> findConflictingReservations(@Param("siteId") Long siteId,
                                                   @Param("spaceId") Long spaceId,
                                                   @Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * Check for capacity conflicts - get overlapping reservations with their people count
     */
    @Query("SELECT r FROM Reservation r " +
           "LEFT JOIN FETCH r.space s " +
           "WHERE r.siteId = :siteId AND r.space.spaceId = :spaceId AND " +
           "r.status NOT IN ('CANCELLED') AND " +
           "((r.startTime < :endTime AND r.endTime > :startTime))")
    List<Reservation> findOverlappingReservationsWithSpace(@Param("siteId") Long siteId,
                                                            @Param("spaceId") Long spaceId,
                                                            @Param("startTime") LocalDateTime startTime,
                                                            @Param("endTime") LocalDateTime endTime);

    /**
     * Count reservations by site (only future reservations)
     */
    @Query("SELECT COUNT(r) FROM Reservation r WHERE r.siteId = :siteId AND r.startTime > :now AND r.status NOT IN ('CANCELLED')")
    long countBySiteId(@Param("siteId") Long siteId, @Param("now") LocalDateTime now);

    /**
     * Count reservations by site and status (only future reservations)
     */
    @Query("SELECT COUNT(r) FROM Reservation r WHERE r.siteId = :siteId AND r.startTime > :now AND r.status = :status")
    long countBySiteIdAndStatus(@Param("siteId") Long siteId, @Param("now") LocalDateTime now, @Param("status") ReservationStatus status);

    /**
     * Count upcoming reservations by site (only confirmed reservations starting in the future)
     */
    @Query("SELECT COUNT(r) FROM Reservation r WHERE r.siteId = :siteId AND r.startTime > :now AND r.status = 'CONFIRMED'")
    long countUpcomingReservations(@Param("siteId") Long siteId, @Param("now") LocalDateTime now);

    /**
     * Count today's reservations by site
     */
    @Query("SELECT COUNT(r) FROM Reservation r WHERE r.siteId = :siteId AND r.startTime >= :startOfDay AND r.startTime <= :endOfDay AND r.status NOT IN ('CANCELLED')")
    long countTodayReservations(@Param("siteId") Long siteId, @Param("startOfDay") LocalDateTime startOfDay, @Param("endOfDay") LocalDateTime endOfDay);

    /**
     * Get current occupancy by site
     */
    @Query("SELECT COUNT(r) FROM Reservation r WHERE r.siteId = :siteId AND r.startTime <= :now AND r.endTime >= :now AND r.status = 'CONFIRMED'")
    Integer getCurrentOccupancy(@Param("siteId") Long siteId, @Param("now") LocalDateTime now);

    /**
     * Count currently active reservations by site (reservations happening right now)
     */
    @Query("SELECT COUNT(r) FROM Reservation r WHERE r.siteId = :siteId AND r.startTime <= :now AND r.endTime >= :now AND r.status = 'CONFIRMED'")
    long countCurrentlyActiveReservations(@Param("siteId") Long siteId, @Param("now") LocalDateTime now);

    /**
     * Count total reservations this week by site (confirmed reservations)
     */
    @Query("SELECT COUNT(r) FROM Reservation r WHERE r.siteId = :siteId AND r.startTime >= :startOfWeek AND r.startTime <= :endOfWeek AND r.status = 'CONFIRMED'")
    long countReservationsThisWeek(@Param("siteId") Long siteId, @Param("startOfWeek") LocalDateTime startOfWeek, @Param("endOfWeek") LocalDateTime endOfWeek);

    /**
     * Count completed reservations this week by site (confirmed reservations that have ended)
     */
    @Query("SELECT COUNT(r) FROM Reservation r WHERE r.siteId = :siteId AND r.endTime >= :startOfWeek AND r.endTime <= :now AND r.status = 'CONFIRMED'")
    long countCompletedReservationsThisWeek(@Param("siteId") Long siteId, @Param("startOfWeek") LocalDateTime startOfWeek, @Param("now") LocalDateTime now);

    /**
     * Find active reservations for a specific space at a specific time
     */
    @Query("SELECT r FROM Reservation r WHERE r.siteId = :siteId AND r.space.spaceId = :spaceId AND " +
           "r.startTime <= :dateTime AND r.endTime > :dateTime AND r.status = 'CONFIRMED'")
    List<Reservation> findActiveReservationsForSpace(@Param("siteId") Long siteId,
                                                      @Param("spaceId") Long spaceId,
                                                      @Param("dateTime") LocalDateTime dateTime);

    /**
     * Check if a space has any reservations (for deletion validation)
     */
    boolean existsBySpace_SpaceId(Long spaceId);

    /**
     * Check if a member has any reservations (for deletion validation)
     */
    boolean existsByMember_MemberId(Long memberId);

    /**
     * Check if a site has any reservations (for deletion validation)
     */
    boolean existsBySiteId(Long siteId);
}
