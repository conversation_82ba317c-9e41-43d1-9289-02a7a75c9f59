package com.workeem.workeem_api.business.space.application;

import com.workeem.workeem_api.business.space.domain.DayOfWeek;
import com.workeem.workeem_api.business.space.domain.Equipment;
import com.workeem.workeem_api.business.space.domain.Space;
import com.workeem.workeem_api.business.space.domain.SpaceStatus;
import com.workeem.workeem_api.business.space.domain.SpaceType;
import com.workeem.workeem_api.business.space.domain.SpaceWeeklySchedule;
import com.workeem.workeem_api.business.space.infrastructure.EquipmentRepository;
import com.workeem.workeem_api.business.space.infrastructure.SpaceRepository;
import com.workeem.workeem_api.business.space.infrastructure.SpaceWeeklyScheduleRepository;
import com.workeem.workeem_api.business.space.web.dto.EquipmentDto;
import com.workeem.workeem_api.business.space.web.dto.SpaceAvailabilityDto;
import com.workeem.workeem_api.business.space.web.dto.SpaceSearchDto;
import com.workeem.workeem_api.business.space.web.dto.SpaceWeeklyScheduleDto;
import com.workeem.workeem_api.business.reservation.infrastructure.ReservationRepository;
import com.workeem.workeem_api.common.exception.CannotDeleteException;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class SpaceServiceImpl implements SpaceService {

    private final SpaceRepository spaceRepository;
    private final EquipmentRepository equipmentRepository;
    private final SpaceWeeklyScheduleRepository spaceWeeklyScheduleRepository;
    private final ReservationRepository reservationRepository;

    @Override
    @Transactional(readOnly = true)
    public List<Space> getAllSpaces(String siteId) {
        log.debug("Fetching all spaces for site: {}", siteId);
        return spaceRepository.findBySiteId(Long.parseLong(siteId));
    }

    @Override
    @Transactional(readOnly = true)
    public List<Space> getActiveSpaces(String siteId) {
        log.debug("Fetching active spaces for site: {}", siteId);
        return spaceRepository.findBySiteIdAndIsActiveTrue(Long.parseLong(siteId));
    }

    @Override
    @Transactional(readOnly = true)
    public Space getSpaceById(Long id) {
        log.debug("Fetching space with ID: {}", id);
        return spaceRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Space with ID " + id + " not found"));
    }

    @Override
    @Transactional(readOnly = true)
    public List<Space> getSpacesByType(String siteId, SpaceType type) {
        log.debug("Fetching spaces by type {} for site: {}", type, siteId);
        return spaceRepository.findBySiteIdAndTypeAndIsActiveTrue(Long.parseLong(siteId), type);
    }

    @Override
    public Space createSpace(Space space) {
        log.debug("Creating new space: {}", space.getName());
        
        // Vérifier si le nom existe déjà pour ce site
        if (existsByName(space.getSiteId().toString(), space.getName())) {
            throw new IllegalArgumentException("Space name already exists for this site");
        }
        
        // Définir les valeurs par défaut
        if (space.getIsActive() == null) {
            space.setIsActive(true);
        }
        
        return spaceRepository.save(space);
    }

    @Override
    public Space createSpace(Space space, List<EquipmentDto> equipmentDtos) {
        return createSpace(space, equipmentDtos, null);
    }

    public Space createSpace(Space space, List<EquipmentDto> equipmentDtos, SpaceAvailabilityDto availabilityDto) {
        log.debug("Creating new space with equipment and availability: {}", space.getName());

        // Créer l'espace d'abord
        Space createdSpace = createSpace(space);

        // Créer les équipements si fournis
        if (equipmentDtos != null && !equipmentDtos.isEmpty()) {
            for (EquipmentDto equipmentDto : equipmentDtos) {
                Equipment equipment = new Equipment();
                equipment.setSpaceId(createdSpace.getSpaceId());
                equipment.setName(equipmentDto.getName());
                equipment.setType(equipmentDto.getType());
                equipment.setBrand(equipmentDto.getBrand());
                equipment.setModel(equipmentDto.getModel());
                equipment.setQuantity(equipmentDto.getQuantity() != null ? equipmentDto.getQuantity() : 1);
                equipment.setStatus(equipmentDto.getStatus() != null ? equipmentDto.getStatus() : com.workeem.workeem_api.business.space.domain.EquipmentStatus.WORKING);
                equipment.setDescription(equipmentDto.getDescription());

                equipmentRepository.save(equipment);
            }
        }

        // Créer les disponibilités si fournies
        if (availabilityDto != null) {
            updateSpaceAvailability(createdSpace.getSpaceId(), availabilityDto);
        } else {
            // Créer un planning par défaut
            createDefaultWeeklySchedule(createdSpace.getSpaceId());
        }

        return createdSpace;
    }

    @Override
    public Space updateSpace(Long spaceId, Space space) {
        log.debug("Updating space with ID: {}", spaceId);
        
        Space existingSpace = getSpaceById(spaceId);
        
        // Vérifier si le nouveau nom existe déjà (sauf pour l'espace actuel)
        if (!existingSpace.getName().equals(space.getName()) &&
            existsByName(space.getSiteId().toString(), space.getName())) {
            throw new IllegalArgumentException("Space name already exists for this site");
        }
        
        // Mettre à jour les champs
        existingSpace.setName(space.getName());
        existingSpace.setDescription(space.getDescription());
        existingSpace.setType(space.getType());
        existingSpace.setCapacity(space.getCapacity());
        existingSpace.setLocation(space.getLocation());
        existingSpace.setFloor(space.getFloor());
        existingSpace.setArea(space.getArea());
        existingSpace.setAmenities(space.getAmenities());
        existingSpace.setRules(space.getRules());
        existingSpace.setImages(space.getImages());

        // Mettre à jour le statut actif
        if (space.getIsActive() != null) {
            existingSpace.setIsActive(space.getIsActive());
        }

        // Mettre à jour les tarifs
        existingSpace.setHourlyRate(space.getHourlyRate());
        existingSpace.setDailyRate(space.getDailyRate());
        existingSpace.setWeeklyRate(space.getWeeklyRate());
        existingSpace.setMonthlyRate(space.getMonthlyRate());
        
        return spaceRepository.save(existingSpace);
    }

    @Override
    public Space updateSpace(Long spaceId, Space space, List<EquipmentDto> equipmentDtos) {
        log.debug("Updating space with equipment: {}", spaceId);

        // Mettre à jour l'espace d'abord
        Space updatedSpace = updateSpace(spaceId, space);

        // Supprimer les anciens équipements
        equipmentRepository.deleteBySpaceId(spaceId);

        // Créer les nouveaux équipements si fournis
        if (equipmentDtos != null && !equipmentDtos.isEmpty()) {
            for (EquipmentDto equipmentDto : equipmentDtos) {
                Equipment equipment = new Equipment();
                equipment.setSpaceId(spaceId);
                equipment.setName(equipmentDto.getName());
                equipment.setType(equipmentDto.getType());
                equipment.setBrand(equipmentDto.getBrand());
                equipment.setModel(equipmentDto.getModel());
                equipment.setQuantity(equipmentDto.getQuantity() != null ? equipmentDto.getQuantity() : 1);
                equipment.setStatus(equipmentDto.getStatus() != null ? equipmentDto.getStatus() : com.workeem.workeem_api.business.space.domain.EquipmentStatus.WORKING);
                equipment.setDescription(equipmentDto.getDescription());

                equipmentRepository.save(equipment);
            }
        }

        return updatedSpace;
    }

    @Override
    public Space updateSpace(Long spaceId, Space space, List<EquipmentDto> equipmentDtos, SpaceAvailabilityDto availabilityDto) {
        log.debug("Updating space with equipment and availability: {}", spaceId);

        // Mettre à jour l'espace et les équipements d'abord
        Space updatedSpace = updateSpace(spaceId, space, equipmentDtos);

        // Mettre à jour la disponibilité si fournie
        if (availabilityDto != null) {
            updateSpaceAvailability(spaceId, availabilityDto);
        }

        return updatedSpace;
    }

    @Override
    public void deleteSpace(Long id) {
        log.debug("Deleting space with ID: {}", id);

        // Vérifier si l'espace a des réservations
        if (reservationRepository.existsBySpace_SpaceId(id)) {
            throw new CannotDeleteException("Impossible de supprimer cet espace car il a des réservations associées. Veuillez d'abord supprimer ou déplacer les réservations.");
        }

        Space space = getSpaceById(id);
        spaceRepository.delete(space);
    }

    @Override
    public Space toggleSpaceActiveStatus(Long spaceId) {
        log.debug("Toggling active status for space with ID: {}", spaceId);

        Space space = getSpaceById(spaceId);
        space.setIsActive(!space.getIsActive());

        return spaceRepository.save(space);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByName(String siteId, String name) {
        return spaceRepository.existsBySiteIdAndNameIgnoreCase(Long.parseLong(siteId), name);
    }

    @Override
    @Transactional(readOnly = true)
    public long countSpacesBySite(String siteId) {
        return spaceRepository.countBySiteId(Long.parseLong(siteId));
    }

    @Override
    @Transactional(readOnly = true)
    public long countActiveSpacesBySite(String siteId) {
        log.debug("Counting active spaces for site: {}", siteId);
        return spaceRepository.countBySiteIdAndIsActiveTrue(Long.parseLong(siteId));
    }

    @Override
    @Transactional(readOnly = true)
    public Integer getTotalCapacityBySite(Long siteId) {
        log.debug("Getting total capacity for site: {}", siteId);
        return spaceRepository.getTotalCapacityBySite(siteId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EquipmentDto> getEquipmentBySpaceId(Long spaceId) {
        log.debug("Fetching equipment for space: {}", spaceId);
        List<Equipment> equipment = equipmentRepository.findBySpaceId(spaceId);

        return equipment.stream()
                .map(eq -> EquipmentDto.builder()
                        .id(eq.getEquipmentId())
                        .name(eq.getName())
                        .type(eq.getType())
                        .brand(eq.getBrand())
                        .model(eq.getModel())
                        .quantity(eq.getQuantity())
                        .status(eq.getStatus())
                        .description(eq.getDescription())
                        .build())
                .collect(java.util.stream.Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public SpaceAvailabilityDto getAvailabilityBySpaceId(Long spaceId) {
        log.debug("Fetching availability for space: {}", spaceId);

        Space space = spaceRepository.findById(spaceId)
                .orElseThrow(() -> new EntityNotFoundException("Space not found with id: " + spaceId));

        List<SpaceWeeklySchedule> schedules = spaceWeeklyScheduleRepository.findBySpaceIdOrderByDay(spaceId);
        List<SpaceWeeklyScheduleDto> scheduleDtos = schedules.stream()
                .map(schedule -> SpaceWeeklyScheduleDto.builder()
                        .day(schedule.getDay())
                        .isOpen(schedule.getIsOpen())
                        .openTime(schedule.getOpenTime())
                        .closeTime(schedule.getCloseTime())
                        .build())
                .collect(Collectors.toList());

        return SpaceAvailabilityDto.builder()
                .advanceBookingDays(space.getAdvanceBookingDays())
                .minBookingDuration(space.getMinBookingDuration())
                .maxBookingDuration(space.getMaxBookingDuration())
                .weeklySchedule(scheduleDtos)
                .build();
    }

    @Override
    public void updateSpaceAvailability(Long spaceId, SpaceAvailabilityDto availabilityDto) {
        log.debug("Updating availability for space: {}", spaceId);

        // Mettre à jour les champs dans l'entité Space
        Space space = spaceRepository.findById(spaceId)
                .orElseThrow(() -> new EntityNotFoundException("Space not found with id: " + spaceId));

        space.setAdvanceBookingDays(availabilityDto.getAdvanceBookingDays() != null ? availabilityDto.getAdvanceBookingDays() : 30);
        space.setMinBookingDuration(availabilityDto.getMinBookingDuration() != null ? availabilityDto.getMinBookingDuration() : 60);
        space.setMaxBookingDuration(availabilityDto.getMaxBookingDuration() != null ? availabilityDto.getMaxBookingDuration() : 480);

        spaceRepository.save(space);

        // Supprimer les anciens horaires
        spaceWeeklyScheduleRepository.deleteBySpaceId(spaceId);

        // Créer les nouveaux horaires
        if (availabilityDto.getWeeklySchedule() != null && !availabilityDto.getWeeklySchedule().isEmpty()) {
            List<SpaceWeeklySchedule> schedules = availabilityDto.getWeeklySchedule().stream()
                    .map(scheduleDto -> {
                        SpaceWeeklySchedule schedule = new SpaceWeeklySchedule();
                        schedule.setSpaceId(spaceId);
                        schedule.setDay(scheduleDto.getDay());
                        schedule.setIsOpen(scheduleDto.getIsOpen());
                        schedule.setOpenTime(scheduleDto.getOpenTime());
                        schedule.setCloseTime(scheduleDto.getCloseTime());
                        return schedule;
                    })
                    .collect(Collectors.toList());

            spaceWeeklyScheduleRepository.saveAll(schedules);
        } else {
            // Créer un planning par défaut
            createDefaultWeeklySchedule(spaceId);
        }
    }

    /**
     * Create default weekly schedule for a space
     */
    private void createDefaultWeeklySchedule(Long spaceId) {
        log.debug("Creating default weekly schedule for space: {}", spaceId);

        List<SpaceWeeklySchedule> schedules = new ArrayList<>();

        // Lundi à Vendredi : ouvert de 9h à 18h
        for (DayOfWeek day : List.of(DayOfWeek.MONDAY, DayOfWeek.TUESDAY, DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY, DayOfWeek.FRIDAY)) {
            SpaceWeeklySchedule schedule = new SpaceWeeklySchedule();
            schedule.setSpaceId(spaceId);
            schedule.setDay(day);
            schedule.setIsOpen(true);
            schedule.setOpenTime(LocalTime.of(9, 0));
            schedule.setCloseTime(LocalTime.of(18, 0));
            schedules.add(schedule);
        }

        // Weekend : fermé
        for (DayOfWeek day : List.of(DayOfWeek.SATURDAY, DayOfWeek.SUNDAY)) {
            SpaceWeeklySchedule schedule = new SpaceWeeklySchedule();
            schedule.setSpaceId(spaceId);
            schedule.setDay(day);
            schedule.setIsOpen(false);
            schedules.add(schedule);
        }

        spaceWeeklyScheduleRepository.saveAll(schedules);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Space> searchSpaces(SpaceSearchDto searchDto) {
        log.debug("Searching spaces with filters: {}", searchDto);

        Long siteId = Long.parseLong(searchDto.getSiteId());

        return spaceRepository.searchSpaces(
            siteId,
            searchDto.getSearch(),
            searchDto.getType(),
            searchDto.getLocation(),
            searchDto.getFloor(),
            searchDto.getMinCapacity(),
            searchDto.getActiveOnly()
        );
    }
}
