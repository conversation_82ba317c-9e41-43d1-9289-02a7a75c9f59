package com.workeem.workeem_api.business.reservation.web.validation;

import com.workeem.workeem_api.business.reservation.web.dto.ReservationRequestDto;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * Validateur pour la logique conditionnelle du nombre de personnes
 */
public class NumberOfPeopleValidator implements ConstraintValidator<ValidNumberOfPeople, ReservationRequestDto> {

    @Override
    public void initialize(ValidNumberOfPeople constraintAnnotation) {
        // Pas d'initialisation nécessaire
    }

    @Override
    public boolean isValid(ReservationRequestDto dto, ConstraintValidatorContext context) {
        if (dto == null) {
            return true; // Laisser d'autres validations gérer les valeurs nulles
        }

        Boolean isFullSpaceReservation = dto.getIsFullSpaceReservation();
        Integer numberOfPeople = dto.getNumberOfPeople();

        // Si isFullSpaceReservation est null, on considère que c'est true par défaut
        boolean isFullReservation = isFullSpaceReservation == null || isFullSpaceReservation;

        if (!isFullReservation) {
            // Pour une réservation partielle, numberOfPeople est obligatoire et doit être positif
            if (numberOfPeople == null || numberOfPeople <= 0) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate(
                    "Le nombre de personnes est obligatoire et doit être positif pour une réservation partielle"
                ).addPropertyNode("numberOfPeople").addConstraintViolation();
                return false;
            }
        } else {
            // Pour une réservation complète, numberOfPeople peut être null ou positif
            if (numberOfPeople != null && numberOfPeople <= 0) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate(
                    "Le nombre de personnes doit être positif s'il est spécifié"
                ).addPropertyNode("numberOfPeople").addConstraintViolation();
                return false;
            }
        }

        return true;
    }
}
