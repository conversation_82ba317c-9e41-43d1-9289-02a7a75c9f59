package com.workeem.workeem_api.business.reservation.web.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * Validation personnalisée pour le nombre de personnes selon le type de réservation
 * - Si isFullSpaceReservation = true : numberOfPeople peut être null ou positif
 * - Si isFullSpaceReservation = false : numberOfPeople doit être obligatoire et positif
 */
@Documented
@Constraint(validatedBy = NumberOfPeopleValidator.class)
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidNumberOfPeople {
    String message() default "Le nombre de personnes est obligatoire pour une réservation partielle";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
