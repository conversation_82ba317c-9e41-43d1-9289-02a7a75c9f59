package com.workeem.workeem_api.business.dashboard.web.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DashboardStatsDto {

    // Statistiques des membres
    private Long totalMembers;
    private Long activeMembers;
    private Long newMembersThisWeek; // Nouveaux membres cette semaine

    // Statistiques des espaces
    private Long totalSpaces;
    private Long availableSpaces;

    // Statistiques des réservations
    private Long activeReservations; // Réservations en cours maintenant
    private Long totalReservationsThisWeek; // Total réservations cette semaine
    private Long completedReservationsThisWeek; // Réservations terminées cette semaine

    // Statistiques financières
    private Double monthlyRevenue;

    // Statistiques des abonnements
    private Long activeSubscriptions;
    
    // Taux d'occupation
    private Double occupancyRate;
    private Integer currentOccupancy;
    private Integer totalCapacity;
    
    // Métadonnées
    private String siteId;
    private String siteName;
    private Long calculatedAt; // timestamp
}
