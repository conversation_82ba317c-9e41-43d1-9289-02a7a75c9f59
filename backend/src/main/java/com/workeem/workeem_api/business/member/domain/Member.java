package com.workeem.workeem_api.business.member.domain;

import com.workeem.workeem_api.shared.common.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

import java.io.Serializable;
import java.time.LocalDateTime;

@Entity
@Table(name = "members")
@SQLDelete(sql = "UPDATE members SET deleted_date = NOW() WHERE member_id = ?")
@SQLRestriction("deleted_date IS NULL")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class Member extends BaseEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "member_id")
    private Long memberId;

    @Column(name = "site_id", nullable = false)
    private Long siteId;

    @Column(name = "first_name", nullable = false, length = 50)
    private String firstName;

    @Column(name = "last_name", nullable = false, length = 50)
    private String lastName;

    @Column(name = "email", nullable = false, length = 100)
    private String email;

    @Column(name = "phone", length = 20)
    private String phone;

    @Column(name = "company", length = 100)
    private String company;

    @Enumerated(EnumType.STRING)
    @Column(name = "member_type", nullable = false)
    private MemberType memberType = MemberType.PROFESSIONAL;

    @Column(name = "student_code", length = 20)
    private String studentCode; // Code étudiant pour les étudiants

    @Column(name = "ice_number", length = 20)
    private String iceNumber; // Numéro ICE pour les entreprises

    @Column(name = "subscription_id")
    private Long subscriptionId; // ID de l'abonnement

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private MemberStatus status = MemberStatus.ACTIVE;

    @Column(name = "deleted_date")
    private LocalDateTime deletedDate;

    // Méthode de compatibilité pour isActive (pour ne pas casser l'existant)
    public Boolean getIsActive() {
        return status == MemberStatus.ACTIVE;
    }

    public void setIsActive(Boolean isActive) {
        this.status = isActive ? MemberStatus.ACTIVE : MemberStatus.INACTIVE;
    }

    // Méthode utilitaire pour obtenir le nom complet
    public String getFullName() {
        return firstName + " " + lastName;
    }
}
