package com.workeem.workeem_api.business.member.infrastructure;

import com.workeem.workeem_api.business.member.domain.Member;
import com.workeem.workeem_api.business.member.domain.MemberType;
import com.workeem.workeem_api.business.member.domain.MemberStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface MemberRepository extends JpaRepository<Member, Long> {

    /**
     * Find all members for a specific site
     */
    List<Member> findBySiteId(Long siteId);



    /**
     * Find member by email and site
     */
    Optional<Member> findBySiteIdAndEmail(Long siteId, String email);

    /**
     * Check if email exists in site
     */
    boolean existsBySiteIdAndEmail(Long siteId, String email);

    /**
     * Count members by site
     */
    long countBySiteId(Long siteId);

    /**
     * Count members by site and status
     */
    long countBySiteIdAndStatus(Long siteId, MemberStatus status);

    /**
     * Count new members added this week by site
     */
    @Query("SELECT COUNT(m) FROM Member m WHERE m.siteId = :siteId AND m.createdDate >= :startOfWeek AND m.createdDate <= :endOfWeek")
    long countNewMembersThisWeek(@Param("siteId") Long siteId, @Param("startOfWeek") LocalDateTime startOfWeek, @Param("endOfWeek") LocalDateTime endOfWeek);

    /**
     * Find all members for a specific site with pagination
     */
    Page<Member> findBySiteId(Long siteId, Pageable pageable);

    /**
     * Find all members for a specific site with pagination and proper date sorting
     * Uses COALESCE to handle NULL lastModifiedDate values
     */
    @Query("SELECT m FROM Member m WHERE m.siteId = :siteId " +
           "ORDER BY COALESCE(m.lastModifiedDate, m.createdDate) DESC")
    Page<Member> findBySiteIdOrderByModifiedDateDesc(@Param("siteId") Long siteId, Pageable pageable);

    @Query("SELECT m FROM Member m WHERE m.siteId = :siteId " +
           "ORDER BY COALESCE(m.lastModifiedDate, m.createdDate) ASC")
    Page<Member> findBySiteIdOrderByModifiedDateAsc(@Param("siteId") Long siteId, Pageable pageable);

    /**
     * Find members by status for a specific site with pagination
     */
    Page<Member> findBySiteIdAndStatus(Long siteId, MemberStatus status, Pageable pageable);

    /**
     * Find members by subscription for a specific site with pagination
     */
    Page<Member> findBySiteIdAndSubscriptionId(Long siteId, Long subscriptionId, Pageable pageable);

    /**
     * Search members by name, email, or other fields with optional filters
     */
    @Query("SELECT m FROM Member m WHERE m.siteId = :siteId " +
           "AND (LOWER(m.firstName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "OR LOWER(m.lastName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "OR LOWER(m.email) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "OR LOWER(m.company) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "OR LOWER(m.studentCode) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "OR LOWER(m.iceNumber) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) " +
           "AND (:status IS NULL OR m.status = :status) " +
           "AND (:memberType IS NULL OR m.memberType = :memberType) " +
           "AND (:subscriptionId IS NULL OR m.subscriptionId = :subscriptionId)")
    Page<Member> searchMembers(@Param("siteId") Long siteId,
                              @Param("searchTerm") String searchTerm,
                              @Param("status") MemberStatus status,
                              @Param("memberType") MemberType memberType,
                              @Param("subscriptionId") Long subscriptionId,
                              Pageable pageable);

    /**
     * Search members with proper date sorting using COALESCE
     */
    @Query("SELECT m FROM Member m WHERE m.siteId = :siteId " +
           "AND (LOWER(m.firstName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "OR LOWER(m.lastName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "OR LOWER(m.email) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "OR LOWER(m.company) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "OR LOWER(m.studentCode) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "OR LOWER(m.iceNumber) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) " +
           "AND (:status IS NULL OR m.status = :status) " +
           "AND (:memberType IS NULL OR m.memberType = :memberType) " +
           "AND (:subscriptionId IS NULL OR m.subscriptionId = :subscriptionId) " +
           "ORDER BY COALESCE(m.lastModifiedDate, m.createdDate) DESC")
    Page<Member> searchMembersOrderByModifiedDateDesc(@Param("siteId") Long siteId,
                                                     @Param("searchTerm") String searchTerm,
                                                     @Param("status") MemberStatus status,
                                                     @Param("memberType") MemberType memberType,
                                                     @Param("subscriptionId") Long subscriptionId,
                                                     Pageable pageable);

    @Query("SELECT m FROM Member m WHERE m.siteId = :siteId " +
           "AND (LOWER(m.firstName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "OR LOWER(m.lastName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "OR LOWER(m.email) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "OR LOWER(m.company) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "OR LOWER(m.studentCode) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "OR LOWER(m.iceNumber) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) " +
           "AND (:status IS NULL OR m.status = :status) " +
           "AND (:memberType IS NULL OR m.memberType = :memberType) " +
           "AND (:subscriptionId IS NULL OR m.subscriptionId = :subscriptionId) " +
           "ORDER BY COALESCE(m.lastModifiedDate, m.createdDate) ASC")
    Page<Member> searchMembersOrderByModifiedDateAsc(@Param("siteId") Long siteId,
                                                    @Param("searchTerm") String searchTerm,
                                                    @Param("status") MemberStatus status,
                                                    @Param("memberType") MemberType memberType,
                                                    @Param("subscriptionId") Long subscriptionId,
                                                    Pageable pageable);

    /**
     * Find members with filters (status, memberType, subscriptionId) without search
     */
    @Query("SELECT m FROM Member m WHERE m.siteId = :siteId " +
           "AND (:status IS NULL OR m.status = :status) " +
           "AND (:memberType IS NULL OR m.memberType = :memberType) " +
           "AND (:subscriptionId IS NULL OR m.subscriptionId = :subscriptionId)")
    Page<Member> findMembersWithFilters(@Param("siteId") Long siteId,
                                       @Param("status") MemberStatus status,
                                       @Param("memberType") MemberType memberType,
                                       @Param("subscriptionId") Long subscriptionId,
                                       Pageable pageable);

    /**
     * Find members with filters and proper date sorting using COALESCE
     */
    @Query("SELECT m FROM Member m WHERE m.siteId = :siteId " +
           "AND (:status IS NULL OR m.status = :status) " +
           "AND (:memberType IS NULL OR m.memberType = :memberType) " +
           "AND (:subscriptionId IS NULL OR m.subscriptionId = :subscriptionId) " +
           "ORDER BY COALESCE(m.lastModifiedDate, m.createdDate) DESC")
    Page<Member> findMembersWithFiltersOrderByModifiedDateDesc(@Param("siteId") Long siteId,
                                                              @Param("status") MemberStatus status,
                                                              @Param("memberType") MemberType memberType,
                                                              @Param("subscriptionId") Long subscriptionId,
                                                              Pageable pageable);

    @Query("SELECT m FROM Member m WHERE m.siteId = :siteId " +
           "AND (:status IS NULL OR m.status = :status) " +
           "AND (:memberType IS NULL OR m.memberType = :memberType) " +
           "AND (:subscriptionId IS NULL OR m.subscriptionId = :subscriptionId) " +
           "ORDER BY COALESCE(m.lastModifiedDate, m.createdDate) ASC")
    Page<Member> findMembersWithFiltersOrderByModifiedDateAsc(@Param("siteId") Long siteId,
                                                             @Param("status") MemberStatus status,
                                                             @Param("memberType") MemberType memberType,
                                                             @Param("subscriptionId") Long subscriptionId,
                                                             Pageable pageable);
}
