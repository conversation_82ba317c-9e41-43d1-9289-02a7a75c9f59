package com.workeem.workeem_api.business.space.web.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpaceAvailabilityDto {

    private Integer advanceBookingDays = 30;
    private Integer minBookingDuration = 60; // en minutes
    private Integer maxBookingDuration = 480; // en minutes

    // Planning hebdomadaire sous forme de liste
    private List<SpaceWeeklyScheduleDto> weeklySchedule;
}
