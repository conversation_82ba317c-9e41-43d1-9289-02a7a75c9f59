package com.workeem.workeem_api.business.reservation.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = ValidMinuteStepValidator.class)
@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidMinuteStep {
    String message() default "Les minutes doivent être des multiples de 30 (00, 30) et les secondes doivent être à 0";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
    int step() default 30;
}
