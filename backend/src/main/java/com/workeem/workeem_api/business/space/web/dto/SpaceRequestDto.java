package com.workeem.workeem_api.business.space.web.dto;

import com.workeem.workeem_api.business.space.domain.Space;
import com.workeem.workeem_api.business.space.domain.Floor;
import com.workeem.workeem_api.business.space.domain.SpaceStatus;
import com.workeem.workeem_api.business.space.domain.SpaceType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpaceRequestDto {

    @NotBlank(message = "Space name is required")
    @Size(max = 100, message = "Space name must not exceed 100 characters")
    private String name;

    @Size(max = 500, message = "Description must not exceed 500 characters")
    private String description;

    private SpaceType type;

    @Min(value = 1, message = "Capacity must be at least 1")
    private Integer capacity;

    @Size(max = 255, message = "Location must not exceed 255 characters")
    private String location;

    private Floor floor;

    @Min(value = 0, message = "Area cannot be negative")
    private BigDecimal area;

    private SpaceStatus status;

    private List<String> images;

    private List<String> amenities;

    private List<String> rules;

    // Pricing fields
    @Min(value = 0, message = "Hourly rate cannot be negative")
    private Double hourlyRate;

    @Min(value = 0, message = "Daily rate cannot be negative")
    private Double dailyRate;

    @Min(value = 0, message = "Weekly rate cannot be negative")
    private Double weeklyRate;

    @Min(value = 0, message = "Monthly rate cannot be negative")
    private Double monthlyRate;

    private Boolean isActive = true;

    // Nested objects for complex data
    private SpaceAvailabilityDto availability;
    private SpacePricingDto pricing;
    private List<EquipmentDto> equipment;

    /**
     * Convert DTO to Entity
     */
    public Space toEntity(Long siteId) {
        Space space = new Space();
        space.setSiteId(siteId);
        space.setName(this.name);
        space.setDescription(this.description);
        space.setType(this.type);
        space.setCapacity(this.capacity);
        space.setLocation(this.location);
        space.setFloor(this.floor);
        space.setArea(this.area);
        space.setStatus(this.status != null ? this.status : SpaceStatus.AVAILABLE);

        // Convert lists to JSON strings (simplified approach)
        if (this.images != null && !this.images.isEmpty()) {
            space.setImages(String.join(",", this.images));
        }
        if (this.amenities != null && !this.amenities.isEmpty()) {
            space.setAmenities(String.join(",", this.amenities));
        }
        if (this.rules != null && !this.rules.isEmpty()) {
            space.setRules(String.join(",", this.rules));
        }

        // Pricing
        space.setHourlyRate(this.hourlyRate);
        space.setDailyRate(this.dailyRate);
        space.setWeeklyRate(this.weeklyRate);
        space.setMonthlyRate(this.monthlyRate);

        // Set availability fields from availability DTO if provided
        if (this.availability != null) {
            space.setAdvanceBookingDays(this.availability.getAdvanceBookingDays() != null ? this.availability.getAdvanceBookingDays() : 30);
            space.setMinBookingDuration(this.availability.getMinBookingDuration() != null ? this.availability.getMinBookingDuration() : 60);
            space.setMaxBookingDuration(this.availability.getMaxBookingDuration() != null ? this.availability.getMaxBookingDuration() : 480);
        } else {
            // Set default values
            space.setAdvanceBookingDays(30);
            space.setMinBookingDuration(60);
            space.setMaxBookingDuration(480);
        }

        space.setIsActive(this.isActive != null ? this.isActive : true);

        return space;
    }

    /**
     * Get equipment DTOs for separate processing
     */
    public List<EquipmentDto> getEquipmentDtos() {
        return this.equipment;
    }

    /**
     * Get availability DTO for separate processing
     */
    public SpaceAvailabilityDto getAvailabilityDto() {
        return this.availability;
    }
}
