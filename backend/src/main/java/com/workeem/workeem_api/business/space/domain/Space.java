package com.workeem.workeem_api.business.space.domain;

import com.workeem.workeem_api.shared.common.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "spaces")
@SQLDelete(sql = "UPDATE spaces SET deleted_date = NOW() WHERE space_id = ?")
@SQLRestriction("deleted_date IS NULL")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class Space extends BaseEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "space_id")
    private Long spaceId;

    @Column(name = "site_id", nullable = false)
    private Long siteId;

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "description", length = 500)
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = true)
    private SpaceType type;

    @Column(name = "capacity")
    private Integer capacity;

    @Column(name = "location")
    private String location;

    @Enumerated(EnumType.STRING)
    @Column(name = "floor")
    private Floor floor;

    @Column(name = "area", precision = 10, scale = 2)
    private BigDecimal area; // en m²

    @Transient
    private SpaceStatus status; // Calculé dynamiquement

    @Column(name = "images", columnDefinition = "TEXT")
    private String images; // JSON string

    @Column(name = "amenities", length = 1000)
    private String amenities; // JSON string

    @Column(name = "rules", columnDefinition = "TEXT")
    private String rules; // JSON string

    @Column(name = "hourly_rate")
    private Double hourlyRate;

    @Column(name = "daily_rate")
    private Double dailyRate;

    @Column(name = "weekly_rate")
    private Double weeklyRate;

    @Column(name = "monthly_rate")
    private Double monthlyRate;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "advance_booking_days", nullable = false)
    private Integer advanceBookingDays = 30;

    @Column(name = "min_booking_duration", nullable = false)
    private Integer minBookingDuration = 60; // en minutes

    @Column(name = "max_booking_duration", nullable = false)
    private Integer maxBookingDuration = 480; // en minutes

    @Column(name = "deleted_date")
    private LocalDateTime deletedDate;

    // Relations
    @OneToMany(mappedBy = "space", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Equipment> equipment;

    @OneToMany(mappedBy = "space", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<SpaceWeeklySchedule> weeklySchedule;

    @OneToOne(mappedBy = "space", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private SpacePricing pricing;

    /**
     * Calcule le statut de l'espace de manière dynamique
     * @return le statut calculé selon l'état actuel
     */
    public SpaceStatus getStatus() {
        // Si l'espace n'est pas actif, il est indisponible
        if (!Boolean.TRUE.equals(this.isActive)) {
            return SpaceStatus.UNAVAILABLE;
        }

        // Si l'espace est actif, vérifier s'il est saturé (capacité maximale atteinte)
        if (isCurrentlySaturated()) {
            return SpaceStatus.SATURATED;
        }

        // Si l'espace est actif, vérifier s'il est occupé maintenant
        if (isCurrentlyOccupied()) {
            return SpaceStatus.OCCUPIED;
        }

        // Sinon, l'espace est disponible
        return SpaceStatus.AVAILABLE;
    }

    /**
     * Setter pour le statut (ne fait rien car le statut est calculé)
     * Conservé pour la compatibilité avec Lombok
     */
    public void setStatus(SpaceStatus status) {
        // Ne fait rien - le statut est calculé dynamiquement
    }

    /**
     * Vérifie si l'espace est actuellement saturé (capacité maximale atteinte)
     * @return true si l'espace est saturé maintenant
     */
    private boolean isCurrentlySaturated() {
        try {
            // Si l'espace n'a pas de capacité définie, il ne peut pas être saturé
            if (this.capacity == null || this.capacity <= 0) {
                return false;
            }

            // Obtenir le contexte Spring pour accéder au service de réservation
            ApplicationContext context = ApplicationContextProvider.getApplicationContext();
            if (context != null) {
                var reservationService = context.getBean("reservationServiceImpl");
                if (reservationService != null) {
                    // Utiliser la réflection pour appeler la méthode
                    var method = reservationService.getClass().getMethod("getCurrentOccupancyForSpace", Long.class, Long.class, LocalDateTime.class);
                    LocalDateTime now = LocalDateTime.now();
                    Integer currentOccupancy = (Integer) method.invoke(reservationService, this.siteId, this.spaceId, now);

                    // L'espace est saturé si l'occupation actuelle >= capacité
                    return currentOccupancy != null && currentOccupancy >= this.capacity;
                }
            }
        } catch (Exception e) {
            // En cas d'erreur, considérer l'espace comme non saturé
            // Log l'erreur si nécessaire
        }
        return false;
    }

    /**
     * Vérifie si l'espace est actuellement occupé par une réservation active
     * @return true si l'espace est occupé maintenant
     */
    private boolean isCurrentlyOccupied() {
        try {
            // Obtenir le contexte Spring pour accéder au service de réservation
            ApplicationContext context = ApplicationContextProvider.getApplicationContext();
            if (context != null) {
                var reservationService = context.getBean("reservationServiceImpl");
                if (reservationService != null) {
                    // Utiliser la réflection pour appeler la méthode
                    var method = reservationService.getClass().getMethod("hasActiveReservationForSpace", Long.class, Long.class, LocalDateTime.class);
                    LocalDateTime now = LocalDateTime.now();
                    return (Boolean) method.invoke(reservationService, this.siteId, this.spaceId, now);
                }
            }
        } catch (Exception e) {
            // En cas d'erreur, considérer l'espace comme disponible
            // Log l'erreur si nécessaire
        }
        return false;
    }
}
