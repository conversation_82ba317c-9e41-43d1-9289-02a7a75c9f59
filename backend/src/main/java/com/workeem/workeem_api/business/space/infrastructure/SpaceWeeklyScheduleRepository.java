package com.workeem.workeem_api.business.space.infrastructure;

import com.workeem.workeem_api.business.space.domain.DayOfWeek;
import com.workeem.workeem_api.business.space.domain.SpaceWeeklySchedule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SpaceWeeklyScheduleRepository extends JpaRepository<SpaceWeeklySchedule, Long> {
    
    /**
     * Find all schedules for a specific space
     */
    List<SpaceWeeklySchedule> findBySpaceIdOrderByDay(Long spaceId);
    
    /**
     * Find schedule for a specific space and day
     */
    Optional<SpaceWeeklySchedule> findBySpaceIdAndDay(Long spaceId, DayOfWeek day);
    
    /**
     * Delete all schedules for a specific space
     */
    @Modifying
    @Query("DELETE FROM SpaceWeeklySchedule sws WHERE sws.spaceId = :spaceId")
    void deleteBySpaceId(@Param("spaceId") Long spaceId);
    
    /**
     * Check if schedule exists for a space and day
     */
    boolean existsBySpaceIdAndDay(Long spaceId, DayOfWeek day);
}
