package com.workeem.workeem_api.business.space.web.dto;

import com.workeem.workeem_api.business.space.domain.DayOfWeek;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpaceWeeklyScheduleDto {
    
    private DayOfWeek day;
    private Boolean isOpen = true;
    private LocalTime openTime;
    private LocalTime closeTime;
}
