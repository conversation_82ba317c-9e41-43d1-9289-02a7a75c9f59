<div class="welcome-container">
  <!-- Page Header -->
  <div class="page-header">
    <h1 class="page-title">
      <nz-icon nzType="dashboard" nzTheme="outline"></nz-icon>
      Tableau de bord
    </h1>
    <p class="page-subtitle">Vue d'ensemble de votre espace de coworking</p>
  </div>

  <!-- Stats Cards -->
  <div class="stats-grid" *ngIf="!loading; else loadingTemplate">
    <!-- Membres actifs -->
    <nz-card class="stat-card" nzHoverable routerLink="/members">
      <div class="stat-content">
        <div class="stat-icon members">
          <nz-icon nzType="team" nzTheme="outline"></nz-icon>
        </div>
        <div class="stat-info">
          <h3>{{ stats.activeMembers || 0 }}</h3>
          <p>Membres actifs</p>
          <small class="stat-detail">{{ stats.totalMembers || 0 }} au total</small>
        </div>
      </div>
    </nz-card>

    <!-- Taux d'occupation -->
    <nz-card class="stat-card" nzHoverable routerLink="/spaces">
      <div class="stat-content">
        <div class="stat-icon occupancy">
          <nz-icon nzType="pie-chart" nzTheme="outline"></nz-icon>
        </div>
        <div class="stat-info">
          <h3>{{ (stats.occupancyRate || 0) | number:'1.1-1' }}%</h3>
          <p>Taux d'occupation</p>
          <small class="stat-detail">{{ stats.currentOccupancy || 0 }}/{{ stats.totalCapacity || 0 }} places</small>
        </div>
      </div>
    </nz-card>

    <!-- Réservations en cours -->
    <nz-card class="stat-card" nzHoverable routerLink="/reservations">
      <div class="stat-content">
        <div class="stat-icon active-reservations">
          <nz-icon nzType="calendar" nzTheme="outline"></nz-icon>
        </div>
        <div class="stat-info">
          <h3>{{ stats.activeReservations || 0 }}</h3>
          <p>Réservations en cours</p>
          <small class="stat-detail">{{ stats.totalReservationsThisWeek || 0 }} cette semaine</small>
        </div>
      </div>
    </nz-card>

    <!-- Abonnements actifs -->
    <nz-card class="stat-card" nzHoverable routerLink="/subscriptions">
      <div class="stat-content">
        <div class="stat-icon subscriptions">
          <nz-icon nzType="credit-card" nzTheme="outline"></nz-icon>
        </div>
        <div class="stat-info">
          <h3>{{ stats.activeSubscriptions || 0 }}</h3>
          <p>Abonnements actifs</p>
          <small class="stat-detail">Plans d'abonnement</small>
        </div>
      </div>
    </nz-card>

    <!-- Espaces disponibles -->
    <nz-card class="stat-card" nzHoverable routerLink="/spaces">
      <div class="stat-content">
        <div class="stat-icon spaces">
          <nz-icon nzType="home" nzTheme="outline"></nz-icon>
        </div>
        <div class="stat-info">
          <h3>{{ stats.availableSpaces || 0 }}</h3>
          <p>Espaces disponibles</p>
          <small class="stat-detail">{{ stats.totalSpaces || 0 }} au total</small>
        </div>
      </div>
    </nz-card>

    <!-- Revenus ce mois -->
    <nz-card class="stat-card" nzHoverable routerLink="/billing">
      <div class="stat-content">
        <div class="stat-icon revenue">
          <nz-icon nzType="dollar" nzTheme="outline"></nz-icon>
        </div>
        <div class="stat-info">
          <h3>{{ (stats.monthlyRevenue || 0) | number:'1.0-0' }} MAD</h3>
          <p>Revenus ce mois</p>
          <small class="stat-detail">Revenus mensuels</small>
        </div>
      </div>
    </nz-card>

    <!-- Nouveaux membres cette semaine -->
    <nz-card class="stat-card" nzHoverable routerLink="/members">
      <div class="stat-content">
        <div class="stat-icon new-members">
          <nz-icon nzType="user-add" nzTheme="outline"></nz-icon>
        </div>
        <div class="stat-info">
          <h3>{{ stats.newMembersThisWeek || 0 }}</h3>
          <p>Nouveaux membres</p>
          <small class="stat-detail">Cette semaine</small>
        </div>
      </div>
    </nz-card>

    <!-- Réservations terminées cette semaine -->
    <nz-card class="stat-card" nzHoverable routerLink="/reservations">
      <div class="stat-content">
        <div class="stat-icon completed-reservations">
          <nz-icon nzType="check-circle" nzTheme="outline"></nz-icon>
        </div>
        <div class="stat-info">
          <h3>{{ stats.completedReservationsThisWeek || 0 }}</h3>
          <p>Réservations terminées</p>
          <small class="stat-detail">Cette semaine</small>
        </div>
      </div>
    </nz-card>
  </div>

  <!-- Template de chargement -->
  <ng-template #loadingTemplate>
    <div class="stats-grid">
      <nz-card class="stat-card" *ngFor="let item of [1,2,3,4,5,6,7,8]">
        <div class="stat-content">
          <div class="stat-icon">
            <nz-spin nzSimple></nz-spin>
          </div>
          <div class="stat-info">
            <h3>---</h3>
            <p>Chargement...</p>
          </div>
        </div>
      </nz-card>
    </div>
  </ng-template>


  <!-- Daily Calendar -->
  <div class="daily-calendar-section" *ngIf="!loading">
    <app-daily-calendar></app-daily-calendar>
  </div>

</div>
