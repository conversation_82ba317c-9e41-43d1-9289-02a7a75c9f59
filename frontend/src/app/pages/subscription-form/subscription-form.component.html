<div class="subscription-form-container">
  <!-- En-tête -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <nz-icon [nzType]="getPageIcon()" nzTheme="outline"></nz-icon>
        {{ getPageTitle() }}
      </h1>
      <p class="page-description">
        {{ isEditMode ? 'Modifiez les informations du plan d\'abonnement' : 'Créez un nouveau plan d\'abonnement avec ses droits et limitations' }}
      </p>
    </div>
    <div class="header-actions">
      <button nz-button nzType="default" (click)="onCancel()" class="cancel-button">
        <nz-icon nzType="arrow-left"></nz-icon>
        Retour
      </button>
    </div>
  </div>

  <!-- Formulaire -->
  <nz-card class="form-card" nzTitle="Informations du plan">
    <nz-spin [nzSpinning]="loading">
      <form nz-form [formGroup]="planForm" (ngSubmit)="onSubmit()">

        <!-- Informations générales -->
        <div class="form-section">
          <h3>Informations générales</h3>

          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzRequired>Nom du plan</nz-form-label>
                <nz-form-control [nzErrorTip]="getErrorMessage('name')">
                  <input
                    nz-input
                    formControlName="name"
                    placeholder="Ex: Étudiant Mensuel"
                    [class.error]="isFieldInvalid('name')"
                  />
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzRequired>Type</nz-form-label>
                <nz-form-control [nzErrorTip]="getErrorMessage('type')">
                  <nz-select
                    formControlName="type"
                    nzPlaceHolder="Sélectionner un type"
                    [class.error]="isFieldInvalid('type')"
                  >
                    <nz-option
                      *ngFor="let option of subscriptionTypeOptions"
                      [nzLabel]="option.label"
                      [nzValue]="option.value"
                    ></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzRequired>Catégories du plan</nz-form-label>
                <nz-form-control [nzErrorTip]="getErrorMessage('membershipTypes')">
                  <nz-select
                    formControlName="membershipTypes"
                    nzMode="multiple"
                    nzPlaceHolder="Sélectionner les catégories"
                    [class.error]="isFieldInvalid('membershipTypes')"
                  >
                    <nz-option
                      *ngFor="let option of membershipTypeOptions"
                      [nzLabel]="option.label"
                      [nzValue]="option.value"
                    ></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="6">
              <nz-form-item>
                <nz-form-label nzRequired>Prix (MAD)</nz-form-label>
                <nz-form-control nzErrorTip="Le prix est requis">
                  <nz-input-number
                    formControlName="price"
                    [nzMin]="0"
                    [nzStep]="10"
                    nzPlaceHolder="0"
                    style="width: 100%"
                  ></nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="6" *ngIf="planForm.get('type')?.value === SubscriptionType.FLEXIBLE">
              <nz-form-item>
                <nz-form-label nzRequired>Durée (jours)</nz-form-label>
                <nz-form-control nzErrorTip="La durée est requise pour le type flexible">
                  <nz-input-number
                    formControlName="duration"
                    [nzMin]="1"
                    [nzStep]="1"
                    nzPlaceHolder="1"
                    style="width: 100%"
                  ></nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <div nz-row [nzGutter]="16" *ngIf="isEditMode">
            <div nz-col nzSpan="24">
              <nz-form-item>
                <nz-form-control>
                  <label nz-checkbox formControlName="isActive">
                    Plan actif
                  </label>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <nz-form-item>
            <nz-form-label>Description</nz-form-label>
            <nz-form-control>
              <textarea
                nz-input
                formControlName="description"
                rows="3"
                placeholder="Description du plan d'abonnement (optionnel)"
              ></textarea>
            </nz-form-control>
          </nz-form-item>
        </div>

        <!-- Droits et limitations -->
        <div class="form-section" formGroupName="rights">
          <h3>Droits et limitations</h3>

          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label>Réservations/jour</nz-form-label>
                <nz-form-control>
                  <nz-input-number
                    formControlName="maxReservationsPerDay"
                    [nzMin]="1"
                    nzPlaceHolder="Ex: 3"
                    style="width: 100%"
                  ></nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label>Réservations/semaine</nz-form-label>
                <nz-form-control>
                  <nz-input-number
                    formControlName="maxReservationsPerWeek"
                    [nzMin]="1"
                    nzPlaceHolder="Ex: 15"
                    style="width: 100%"
                  ></nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="8">
              <nz-form-item>
                <nz-form-label>Réservations/mois</nz-form-label>
                <nz-form-control>
                  <nz-input-number
                    formControlName="maxReservationsPerMonth"
                    [nzMin]="1"
                    nzPlaceHolder="Ex: 60"
                    style="width: 100%"
                  ></nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label>Heures consécutives max</nz-form-label>
                <nz-form-control>
                  <nz-input-number
                    formControlName="maxConsecutiveHours"
                    [nzMin]="1"
                    [nzMax]="24"
                    nzPlaceHolder="Ex: 8"
                    style="width: 100%"
                  ></nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label>Réservation à l'avance (jours)</nz-form-label>
                <nz-form-control>
                  <nz-input-number
                    formControlName="advanceBookingDays"
                    [nzMin]="1"
                    nzPlaceHolder="Ex: 30"
                    style="width: 100%"
                  ></nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzRequired>Jours autorisés</nz-form-label>
                <nz-form-control [nzErrorTip]="getErrorMessage('rights.allowedDays')">
                  <nz-select
                    formControlName="allowedDays"
                    nzMode="multiple"
                    nzPlaceHolder="Sélectionner les jours"
                    [class.error]="isFieldInvalid('rights.allowedDays')"
                  >
                    <nz-option
                      *ngFor="let day of dayOptions"
                      [nzLabel]="day.label"
                      [nzValue]="day.value"
                    ></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label nzRequired>Salles incluses</nz-form-label>
                <nz-form-control [nzErrorTip]="getErrorMessage('rights.includedRooms')">
                  <nz-select
                    formControlName="includedRooms"
                    nzMode="multiple"
                    nzPlaceHolder="Sélectionner les salles"
                    [class.error]="isFieldInvalid('rights.includedRooms')"
                  >
                    <nz-option
                      *ngFor="let room of getRoomOptions()"
                      [nzLabel]="room.label"
                      [nzValue]="room.value"
                    ></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>



          <!-- Créneaux horaires -->
          <nz-form-item>
            <nz-form-label nzRequired>Créneaux horaires autorisés</nz-form-label>
            <nz-form-control>
              <div formArrayName="allowedTimeSlots">
                <div
                  *ngFor="let slot of timeSlots.controls; let i = index"
                  [formGroupName]="i"
                  class="time-slot-row"
                >
                  <nz-time-picker
                    formControlName="start"
                    nzFormat="HH:mm"
                    nzPlaceHolder="Début"
                    [nzMinuteStep]="30"
                    [nzSecondStep]="60"
                    [nzHideDisabledOptions]="true"
                  ></nz-time-picker>
                  <span class="time-separator">à</span>
                  <nz-time-picker
                    formControlName="end"
                    nzFormat="HH:mm"
                    nzPlaceHolder="Fin"
                    [nzMinuteStep]="30"
                    [nzSecondStep]="60"
                    [nzHideDisabledOptions]="true"
                  ></nz-time-picker>
                  <button
                    nz-button
                    nzType="text"
                    nzDanger
                    type="button"
                    (click)="removeTimeSlot(i)"
                    [disabled]="timeSlots.length <= 1"
                  >
                    <nz-icon nzType="delete"></nz-icon>
                  </button>
                </div>
              </div>
              <button
                nz-button
                nzType="dashed"
                type="button"
                (click)="addTimeSlot()"
                class="add-time-slot-btn"
              >
                <nz-icon nzType="plus"></nz-icon>
                Ajouter un créneau
              </button>
            </nz-form-control>
          </nz-form-item>
        </div>

        <!-- Fonctionnalités -->
        <div class="form-section">
          <h3>Fonctionnalités</h3>

          <nz-form-item>
            <nz-form-label>Liste des fonctionnalités</nz-form-label>
            <nz-form-control>
              <div formArrayName="features">
                <div
                  *ngFor="let feature of features.controls; let i = index"
                  class="feature-row"
                >
                  <input
                    nz-input
                    [formControlName]="i"
                    placeholder="Ex: WiFi gratuit"
                  />
                  <button
                    nz-button
                    nzType="text"
                    nzDanger
                    type="button"
                    (click)="removeFeature(i)"
                  >
                    <nz-icon nzType="delete"></nz-icon>
                  </button>
                </div>
              </div>
              <button
                nz-button
                nzType="dashed"
                type="button"
                (click)="addFeature()"
                class="add-feature-btn"
              >
                <nz-icon nzType="plus"></nz-icon>
                Ajouter une fonctionnalité
              </button>
            </nz-form-control>
          </nz-form-item>
        </div>

        <!-- Actions -->
        <div class="form-actions">
          <button nz-button nzType="default" type="button" (click)="onCancel()" class="cancel-action">
            Annuler
          </button>
          <button
            nz-button
            nzType="primary"
            [nzLoading]="loading"
            class="submit-action"
            type="submit"
          >
            {{ isEditMode ? 'Modifier' : 'Créer' }} le plan
          </button>
        </div>
      </form>
    </nz-spin>
  </nz-card>
</div>
