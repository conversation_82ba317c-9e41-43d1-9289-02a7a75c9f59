import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class NoAuthGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(): boolean {
    const isAuthenticated = this.authService.isAuthenticated();
    const tenantId = localStorage.getItem('workeem_tenant_id');

    // Si l'utilisateur est authentifié ET a un tenant, rediriger vers l'app
    if (isAuthenticated && tenantId) {
      this.router.navigate(['/welcome']);
      return false;
    }

    // Si pas authentifié OU pas de tenant, autoriser l'accès à la page de sélection de tenant
    return true;
  }
}
